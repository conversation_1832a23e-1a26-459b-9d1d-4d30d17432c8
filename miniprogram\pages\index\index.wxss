/* ===================================================================
 * 瑜伽馆首页样式文件 - 整理版
 * 参考profile页面字体规范，统一使用rpx单位
 * ================================================================= */

/*
 * 字体规范：
 * - 大字体: 32rpx (标题、重要信息)
 * - 中字体: 28rpx (正文内容)
 * - 小字体: 24rpx (辅助信息、标签)
 * - 统一字体族: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif
 */

/* ===================================================================
 * 1. 基础容器和布局
 * ================================================================= */

/* 页面根容器 */
.container {
  padding: 32rpx;
  padding-bottom: calc(32rpx + 120rpx + env(safe-area-inset-bottom));
  background-color: #f5f5f5;
  min-height: 100vh;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
}

/* 统一卡片样式（背景白 + 24rpx圆角 + 统一阴影） */
.card {
  background-color: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 82, 217, 0.15);
}


/* ===================================================================
 * 2. 头部区域 - 视差滚动容器和Logo标题
 * ================================================================= */

/* 视差滚动容器 */
.parallax-header {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 18vh;
  margin-bottom: 32rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 82, 217, 0.15);
}

/* 视差背景图片 */
.parallax-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  object-fit: contain;
  transition: transform 0.3s;
  transform: translateZ(0);
}

/* 半透明遮罩层 */
.parallax-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background: linear-gradient(to top, rgba(0,0,0,0.5), transparent);
  border-radius: 24rpx;
}

/* 视差内容区 */
.parallax-content {
  position: absolute;
  bottom: 32rpx;
  left: 32rpx;
  display: flex;
  align-items: flex-end;
  z-index: 2;
}

/* Logo图片样式 - 保持特殊设计 */
.logo {
  margin-right: 24rpx;
  border: none;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  border-radius: 16rpx;
  position: relative;
  z-index: 1;
  padding: 6rpx;
  background: rgba(255, 255, 255, 0.8);
}

/* 标题文字样式 - 保持特殊字体大小 */
.title {
  font-size: 42rpx;
  font-weight: 600;
  color: #ffffff;
  letter-spacing: 2rpx;
  position: relative;
  z-index: 1;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.5);
}

/* 微交互：按压效果 */
.interactive-press {
  transform: scale(0.98);
  filter: brightness(0.95);
}

/* ===================================================================
 * 3. 联系信息区域
 * ================================================================= */

/* 联系信息容器 */
.contact-info-container {
  width: 100%;
  margin-bottom: 8rpx;
  padding: 8rpx 0;

  opacity: 0;
  animation: image-fade-in 0.6s ease-out forwards;
  animation-delay: 0.05s;
  transition: transform 0.2s ease-out, filter 0.2s ease-out;
}

/* 联系信息行 */
.contact-row {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 16rpx;
  padding: 20rpx 20rpx 0rpx 20rpx;
  width: 100%;
  box-sizing: border-box;
  flex-wrap: nowrap !important;
}

/* 联系信息项基础样式 */
.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 8rpx;
  min-width: 0;
}

/* 电话信息项 - 只占用必要宽度 */
.contact-item-phone {
  flex: 0 0 auto !important;
  white-space: nowrap;
  min-width: fit-content;
  max-width: none;
  flex-shrink: 0 !important;
  overflow: visible;
}

/* 地址信息项 - 占用剩余空间 */
.contact-item-address {
  flex: 1 1 auto !important;
  min-width: 0;
  max-width: none;
  flex-shrink: 1 !important;
  overflow: hidden;
}

/* 联系信息图标 */
.contact-icon {
  color: #0052d9;
  font-size: 32rpx;
  margin-top: 4rpx;
  flex-shrink: 0;
}

/* 联系信息内容 */
.contact-content {
  position: relative;
  padding-right: 56rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;

}

/* 联系信息标签 - 统一为32rpx黑色 */
.contact-label {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 600;
  flex-shrink: 0;

}

/* 联系信息值 - 统一为28rpx黑色 */
.contact-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  flex: 1;
  min-width: 0;

}

/* 电话号码专用样式 */
.contact-item-phone .contact-value {
  white-space: nowrap;
  overflow: visible;
  word-break: keep-all;
}

/* 地址文本样式 */
.contact-item-address .contact-value {
  word-break: break-word;
  overflow-wrap: break-word;
}

/* ===================================================================
 * 4. 公告系统
 * ================================================================= */

/* 公告容器 */
.announcement-container {
  display: flex;
  align-items: flex-start;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-top: 1rpx solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;

}

.announcement-container:active {
  background-color: #f5f5f5;
}

/* 公告图标 */
.announcement-icon {
  color: #ff6b35;
  font-size: 32rpx;
  margin-top: 4rpx;
  flex-shrink: 0;
}

/* 公告内容 */
.announcement-content {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
  min-width: 0;

}

/* 公告标题区域 */
.announcement-header {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

/* 公告标签 - 修正为32rpx黑色 */
.announcement-label {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  flex-shrink: 0;

}

/* 公告文本 - 修正为28rpx黑色 */
.announcement-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  text-align: justify;
  width: 100%;
  word-wrap: break-word;
  font-weight: 400;
}

/* 限制2行文字的公告文本 - 修正为28rpx黑色 */
.announcement-text-limited {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  margin-right: 48rpx;
  color: #333;
  width: 100%;
  font-size: 28rpx;
  line-height: 1.5;
  font-weight: 400;
}

/* 更多按钮 */
.announcement-more {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%) translateY(1em);
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  padding: 4rpx;
  z-index: 2;
}

/* 多条公告样式 */
.single-announcement {
  width: 100%;

}

.multiple-announcements {
  position: relative;
  width: 100%;
  display: flex;
  align-items: flex-start;
  gap: 16rpx;


}

.announcement-swiper {
  flex: 1;
  height: 84rpx; /* 与两行文本高度匹配，去掉额外空白 */
}

.announcement-swiper-item {
  display: flex;
  align-items: flex-start;
  height: 100%;
  box-sizing: border-box;
}

/* ===================================================================
 * 5. 功能按钮
 * ================================================================= */

/* 功能按钮容器 */
.function-buttons {
  gap: 40rpx;
  width: 100%;
  margin-bottom: 40rpx;
  display: flex;
  justify-content: space-between;
  padding: 0 32rpx;

}

.function-buttons.fixed-bottom {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 32rpx;
  width: 100vw;
  max-width: 100vw;
  box-sizing: border-box;
  left: 0;
  right: 0;
  padding-left: 32rpx;
  padding-right: 32rpx;
}

/* 功能按钮样式 */
.function-btn {
  flex: 1 1 0;
  min-width: 0;
  max-width: 100%;
  border-radius: 24rpx;
  font-weight: 600;
  height: 104rpx;
  line-height: 104rpx;

  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 12rpx 40rpx rgba(0, 82, 217, 0.3), 0 4rpx 16rpx rgba(0, 82, 217, 0.15);
  background: linear-gradient(135deg, #0052D9 0%, #1890ff 100%) !important;
  border: 2rpx solid rgba(255, 255, 255, 0.2) !important;
}

.function-btn:hover {
  transform: translateY(-4rpx) scale(1.02);
  box-shadow: 0 16rpx 48rpx rgba(0, 82, 217, 0.4), 0 8rpx 24rpx rgba(0, 82, 217, 0.2);
  background: linear-gradient(135deg, #1677cc 0%, #40a9ff 100%) !important;
}

.function-btn:active {
  transform: translateY(6rpx) scale(0.98);
  box-shadow: 0 6rpx 24rpx rgba(0, 82, 217, 0.4), 0 2rpx 12rpx rgba(0, 82, 217, 0.2);
  background: linear-gradient(135deg, #003d9e 0%, #1677cc 100%) !important;
}

.function-btn .t-button__content {
  font-size: 28rpx !important;
  color: #ffffff !important;
  font-weight: 600 !important;

}

.function-btn .t-icon {
  font-size: 32rpx !important;
  color: #ffffff !important;
  margin-right: 16rpx;
}

/* ===================================================================
 * 6. 图片展示
 * ================================================================= */

/* 静态图片容器 */
.static-images-container {
  width: 100%;
  margin: 16rpx 0 40rpx 0;
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.static-image {
  width: 100%;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 82, 217, 0.15);
  display: block;
  background-color: #f5f5f5;
}

/* 图片淡入动画 */
@keyframes image-fade-in {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 相册弹出层 */
.album-popup {
  --td-popup-border-radius: 0;
}

.album-popup-content {
  background: transparent;
  border-radius: 0;
  width: 96vw;
  max-width: 100vw;
  max-height: 90vh;
  min-height: 0;
  box-shadow: none;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.album-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 24rpx;
}

.album-close-btn {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255,255,255,0.92);
  border-radius: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.08);
  font-size: 28rpx;
  color: #333;
  padding: 0 24rpx 0 8rpx;
  cursor: pointer;
}

.album-close-btn t-icon {
  margin-right: 8rpx;
}

.album-popup-close {
  position: absolute;
  top: 40rpx;
  left: 40rpx;
  z-index: 100;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(30, 30, 30, 0.65);
  border-radius: 50%;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.18);
  transition: background 0.2s, box-shadow 0.2s;
  cursor: pointer;
}

.album-popup-close:active {
  background: rgba(30, 30, 30, 0.7);
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.28);
}

.album-popup-close t-icon {
  filter: drop-shadow(0 4rpx 8rpx rgba(0,0,0,0.25));
}

/* ===================================================================
 * 7. 弹出层系统
 * ================================================================= */

/* 公告弹出层 */
.announcement-popup {
  --td-popup-border-radius: 48rpx;
  /* 毛玻璃背景遮罩 */
  --td-popup-bg-color: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
}

/* 地址弹出层 */
.address-popup {
  --td-popup-border-radius: 48rpx;
  --td-popup-bg-color: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
}

.popup-content {
  /* 现代化渐变背景 */
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.9) 100%);
  border-radius: 48rpx;
  width: 82vw;
  max-width: 720rpx;
  max-height: 75vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* 高端阴影效果 */
  box-shadow:
    0 32rpx 128rpx rgba(0, 0, 0, 0.15),
    0 8rpx 32rpx rgba(0, 0, 0, 0.1),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.8);
  padding-bottom: 64rpx;
  /* 入场动画 */
  animation: popupSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  /* 毛玻璃边框 */
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

/* Popup入场动画 */
@keyframes popupSlideIn {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(60rpx);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.popup-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 64rpx 56rpx 40rpx 56rpx;
  /* 现代化渐变分割线 */
  border-bottom: 2rpx solid rgba(240, 240, 240, 0.6);
  gap: 24rpx;
  /* 头部背景渐变 */
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%);
  position: relative;
}

/* 头部装饰性光效 */
.popup-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 120rpx;
  height: 8rpx;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(250, 173, 20, 0.6) 50%,
    transparent 100%);
  border-radius: 0 0 8rpx 8rpx;
}

.popup-title-icon {
  color: #faad14;
  /* 图标发光效果 */
  filter: drop-shadow(0 4rpx 12rpx rgba(250, 173, 20, 0.3));
  animation: iconPulse 2s ease-in-out infinite;
}

/* 图标脉冲动画 */
@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.popup-title {
  font-size: 36rpx;
  font-weight: 700;
  /* 现代化文字渐变 */
  background: linear-gradient(135deg, #333 0%, #666 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 1rpx;
}

.popup-body {
  padding: 64rpx 56rpx 0 56rpx;
  flex: 1;
  overflow-y: auto;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(250, 173, 20, 0.3) transparent;
}

/* Webkit滚动条样式 */
.popup-body::-webkit-scrollbar {
  width: 8rpx;
}

.popup-body::-webkit-scrollbar-track {
  background: transparent;
}

.popup-body::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg,
    rgba(250, 173, 20, 0.4) 0%,
    rgba(250, 173, 20, 0.2) 100%);
  border-radius: 8rpx;
}

.popup-text {
  font-size: 30rpx;
  /* 现代化文字颜色 */
  color: #2c3e50;
  line-height: 1.6;
  text-align: justify;
  word-break: break-word;
  white-space: pre-wrap;
  /* 文字阴影增强可读性 */
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
  /* 文字渐入动画 */
  animation: textFadeIn 0.6s ease-out 0.2s both;
}

/* 文字渐入动画 */
@keyframes textFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.indent-text {
  text-indent: 4em;
  display: block;
}

.close-btn {
  position: absolute;
  left: 50%;
  margin-left: -32rpx;
  bottom: -160rpx;
  cursor: pointer;
  transition: transform 0.2s ease;
  z-index: 10;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.10);
}

.close-btn:active {
  transform: scale(0.9);
}

/* 弹窗导航 */
.popup-announcement-nav {
  margin-top: 40rpx;
  padding-top: 32rpx;
  /* 现代化分割线 */
  border-top: 2rpx solid rgba(240, 240, 240, 0.6);
  position: relative;
}

/* 导航区域装饰线 */
.popup-announcement-nav::before {
  content: '';
  position: absolute;
  top: -2rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(250, 173, 20, 0.4) 50%,
    transparent 100%);
  border-radius: 2rpx;
}

.nav-buttons {
  display: flex;
  justify-content: space-between;
  gap: 24rpx;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 24rpx;
  /* 现代化渐变背景 */
  background: linear-gradient(135deg,
    rgba(248, 249, 250, 0.9) 0%,
    rgba(233, 236, 239, 0.8) 100%);
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #495057;
  /* 高端过渡效果 */
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  font-weight: 600;
  /* 现代化阴影 */
  box-shadow:
    0 8rpx 24rpx rgba(0, 0, 0, 0.08),
    0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
}

.nav-btn:hover {
  background: linear-gradient(135deg,
    rgba(250, 173, 20, 0.1) 0%,
    rgba(250, 173, 20, 0.05) 100%);
  transform: translateY(-4rpx) scale(1.02);
  box-shadow:
    0 16rpx 40rpx rgba(0, 0, 0, 0.12),
    0 4rpx 16rpx rgba(250, 173, 20, 0.15);
  color: #333;
}

.nav-btn:active {
  transform: translateY(-2rpx) scale(0.98);
  box-shadow:
    0 8rpx 20rpx rgba(0, 0, 0, 0.08),
    0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.nav-btn.disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none !important;
  background: rgba(248, 249, 250, 0.5) !important;
  box-shadow: none !important;
}

/* 悬停效果类 */
.popup-content-hover {
  transform: scale(1.002);
}

.nav-btn.disabled:hover {
  background-color: #f8f9fa;
  transform: none;
}

/* ===================================================================
 * 8. 课程系统
 * ================================================================= */

/* 区块样式 */
.section {
  margin-bottom: 48rpx;
}

/* 区块标题 */
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 32rpx;
  color: #333;
}

/* 课程卡片容器 */
.course-cards {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

/* 课程卡片 */
.course-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 82, 217, 0.15);
}

/* 课程头部 */
.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

/* 课程标题 */
.course-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 课程状态 */
.course-status {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  flex-shrink: 0;
  white-space: nowrap;
}

.course-status.available {
  background-color: #e8f5e8;
  color: #52c41a;
}

.course-status.full {
  background-color: #fff2e8;
  color: #fa8c16;
}

/* 课程信息列表 */
.course-info-list {
  margin-bottom: 24rpx;
}

/* 课程信息项 */
.course-info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 24rpx;
  color: #666;
}

.course-info-item t-icon {
  margin-right: 16rpx;
  color: #0052d9;
}

/* 课程描述 */
.course-description {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 32rpx;
}

/* 课程操作按钮容器 */
.course-actions {
  display: flex;
  justify-content: flex-end;
}

/* ===================================================================
 * 9. 动画和特效
 * ================================================================= */

/* 呼吸动画 */
@keyframes breathing {
  0%, 100% {
    transform: scale(0.95);
    box-shadow: 0 0 40rpx 10rpx rgba(0, 82, 217, 0.1);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 80rpx 30rpx rgba(0, 82, 217, 0.3);
  }
}


/* ==========================
 * Loading 全屏遮罩与居中样式
 * 说明：之前未定义.loading-container，元素按文档流出现在左上角。
 * 这里使用定位+Flex让其覆盖全屏并在中间显示。
 * ========================== */
/* 加载容器（覆盖整屏并居中） */
.loading-container {
  position: fixed;           /* 定位到视口；常见值：static(默认)、relative、absolute、fixed、sticky。fixed适合做全屏遮罩 */
  top: 0;                    /* 顶部距离为0，配合left/right/bottom占满屏幕；等价于CSS的inset: 0; */
  right: 0;                  /* 右侧距离为0 */
  bottom: 0;                 /* 底部距离为0 */
  left: 0;                   /* 左侧距离为0 */
  display: flex;             /* 使用Flex布局以便快速居中；常见值：block/inline/flex/grid */
  flex-direction: column;    /* 垂直堆叠Logo和文字；常见值：row/column */
  align-items: center;       /* 水平居中子元素（类似HTML/CSS的align-items） */
  justify-content: center;   /* 垂直居中子元素（类似HTML/CSS的justify-content） */
  z-index: 9999;             /* 层级置顶，确保覆盖页面其它内容；常见用途：弹窗/遮罩 */
  background: rgba(255,255,255,0.85); /* 半透明白色背景；可举一反三：黑色遮罩用rgba(0,0,0,0.5) */
  transition: opacity 0.5s ease;      /* 退场动画的过渡时间，与JS里的500ms对应 */
}

/* 退场状态（通过JS设置loadingAnimationState='hiding'添加此类） */
.loading-container.hiding {
  opacity: 0;                /* 渐隐效果 */
  pointer-events: none;      /* 淡出时不拦截点击，避免挡住下面内容 */
}

/* 加载Logo样式（呼吸光影） */
.loading-logo {
  /* 尺寸已在WXML用width/height指定，这里可再次声明以防第三方组件内部样式覆盖 */
  width: 200rpx;             /* rpx为小程序自适应单位，类似移动端的vw按设备宽度缩放 */
  height: 200rpx;
  border-radius: 24rpx;      /* 圆角；常见值：0、8rpx、50%（完全圆形） */
  background: #fff;          /* 白底防止透明Logo与背景融合 */
  box-shadow: 0 40rpx 120rpx rgba(0, 82, 217, 0.35); /* 外发光阴影；可类比CSS的阴影用法 */
  animation: breathing 2.4s ease-in-out infinite;     /* 复用上面定义的breathing关键帧 */
}

/* 加载文字容器 */
.loading-text-container {
  display: flex;
  align-items: center;
  margin-top: 40rpx;
}

/* 加载图标 */
.loading-spinner {
  font-size: 32rpx !important;
  color: #0052d9;
  margin-right: 16rpx;
}

/* 加载文字 */
.loading-text {
  font-size: 28rpx;
  color: #888;
  letter-spacing: 2rpx;
}

/* ===================================================================
 * 10. 工具类和特殊样式
 * ================================================================= */

/* 文本统一样式 - 移除color: inherit避免覆盖颜色设置 */
.text-unify {
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-weight: inherit;
}

/* 地址限制显示样式 - 统一为黑色 */
.contact-value-limited {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  color: #333 !important;
  min-width: 0;
  max-width: 100%;
}

/* 确保地址文本显示为黑色 - 更具体的选择器 */
.contact-item-address .contact-value {
  color: #333 !important;
}

.address-more {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%) translateY(1em);
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  padding: 4rpx;
  z-index: 2;
}

/* 已删除小屏幕适配代码，因为rpx本身就是响应式适配单位 */
