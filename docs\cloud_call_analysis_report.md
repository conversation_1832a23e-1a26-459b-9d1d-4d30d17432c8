# 云服务调用分析报告

本报告详细列出了项目中所有消耗“调用次数”的操作，包括云函数调用 (`wx.cloud.callFunction`) 和所有数据库操作 (`.get`, `.add`, `.update`, `.remove`, `.count`)。

---

### 文件: cloudfunctions\adminManagement\index.js
- L18: }).get();
- L55: }).get();
- L281: const { data: users } = await db.collection(USERS_COLLECTION).get();
- L288: await db.collection(USERS_COLLECTION).doc(user._id).update({
- L395: const result = await db.collection(COURSES_COLLECTION).add({
- L437: const courseResult = await db.collection(COURSES_COLLECTION).doc(courseId).get();
- L466: .get();
- L480: db.collection('bookings').doc(booking._id).update({
- L495: .update({
- L524: await db.collection(COURSES_COLLECTION).doc(courseId).update({
- L609: await db.collection(COURSES_COLLECTION).doc(data._id).update({
- L641: const courseResult = await db.collection(COURSES_COLLECTION).doc(_id).get();
- L666: await db.collection(COURSES_COLLECTION).doc(_id).remove();
- L700: .get();
- L729: const { data: course } = await db.collection(COURSES_COLLECTION).doc(_id).get();
- L818: const userRes = await db.collection('users').where({ openid: db.command.in(allOpenids) }).get();
- L889: const result = await db.collection(MEMBERSHIP_CARD_COLLECTION).add({
- L940: const cardResult = await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).get();
- L966: await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).update({
- L977: await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).update({
- L1017: const cardResult = await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).get();
- L1027: await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).remove();
- L1065: const cardResult = await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).get();
- L1087: }).get();
- L1103: }).get();
- L1113: await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).update({
- L1151: const cardResult = await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).get();
- L1175: await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).update({
- L1211: const cardResult = await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).get();
- L1235: }).get();
- L1245: await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).update({
- L1283: const cardResult = await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).get();
- L1297: await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).update({
- L1332: await db.collection(MEMBERSHIP_CARD_COLLECTION).doc(cardId).update({
- L1390: .get();
- L1403: const userRes = await userQuery.get();
- L1459: await db.collection(USERS_COLLECTION).doc(userId).update({
- L1505: }).get();
- L1515: await db.collection(USERS_COLLECTION).doc(userId).update({
- L1561: const countResult = await query.count();
- L1569: .get();
- L1607: }).get();
- L1655: }).get();
- L1677: }).get();
- L1689: }).get();
- L1699: await db.collection(USERS_COLLECTION).doc(user._id).remove();
- L1732: }).get();
- L1754: }).get();
- L1766: }).get();
- L1776: await db.collection(USERS_COLLECTION).doc(user._id).update({
- L1809: result = await settingsCollection.doc('system_settings').get();
- L1846: await settingsCollection.add({
- L1892: const currentResult = await settingsCollection.doc('system_settings').get();
- L1918: await settingsCollection.doc('system_settings').update({
- L1924: await settingsCollection.add({
- L1964: }).get();
- L2017: }).get();
- L2029: }).get();
- L2035: await db.collection('coachInfo').add({
- L2047: await db.collection('coachInfo').doc(coach._id).update({
- L2105: .get();
- L2131: const result = await db.collection(MEMBERSHIP_CARD_TEMPLATE_COLLECTION).add({
- L2190: .get();
- L2209: await db.collection(MEMBERSHIP_CARD_TEMPLATE_COLLECTION).doc(id).update({
- L2242: const template = await db.collection(MEMBERSHIP_CARD_TEMPLATE_COLLECTION).doc(id).get();
- L2258: await db.collection(MEMBERSHIP_CARD_TEMPLATE_COLLECTION).doc(id).remove();
- L2292: .get();
- L2330: const templateResult = await db.collection(MEMBERSHIP_CARD_TEMPLATE_COLLECTION).doc(templateId).get();
- L2370: }).get();
- L2386: const cardResult = await db.collection(MEMBERSHIP_CARD_COLLECTION).add({
- L2391: await db.collection(MEMBERSHIP_CARD_TEMPLATE_COLLECTION).doc(templateId).update({
- L2512: const courseResult = await db.collection('courses').doc(courseId).get();
- L2561: await db.collection('courses').doc(courseId).update({
- L2589: await db.collection('courses').doc(courseId).update({
- L2622: .count();
- L2632: await db.collection('courses').doc(courseId).remove();

### 文件: cloudfunctions\bookingManagement\index.js
- L22: }).get();
- L98: const courseRes = await db.collection(COURSES).doc(courseId).get();
- L104: .where({ courseId, userId }).get();
- L116: .where({ courseId, status: 'upcoming' }).count();
- L120: const userRes = await db.collection('users').where({ openid: userId }).get();
- L146: .get();
- L164: await db.collection(MEMBERSHIP_CARD).doc(selectedCard._id).update({
- L177: await db.collection(BOOKINGS).doc(bookingId).update({
- L187: bookingResult = await db.collection(BOOKINGS).add({
- L252: .where({ courseId, userId, status: 'upcoming' }).get();
- L260: const courseRes = await db.collection(COURSES).doc(courseId).get();
- L265: const settingsRes = await db.collection('systemSettings').doc('system_settings').get();
- L319: .get();
- L324: await db.collection(MEMBERSHIP_CARD).doc(card._id).update({
- L341: await db.collection(BOOKINGS).doc(booking._id).update({
- L401: .get();
- L425: .get();
- L449: const { data: courses } = await db.collection(COURSES).where({ status: 'online' }).get();
- L451: const { data: bookings } = await db.collection(BOOKINGS).where({ status: 'upcoming' }).get();
- L455: const res = await db.collection(BOOKINGS).where({ userId }).get();
- L463: const userRes = await db.collection('users').where({ openid: db.command.in(allOpenids) }).get();
- L538: }).get();
- L599: .get();
- L607: const userRes = await db.collection('users').where({ openid: db.command.in(uniqueCoachOpenids) }).get();
- L635: .get();
- L642: .get();
- L652: const userRes = await db.collection('users').where({ openid: db.command.in(uniqueCoachOpenids) }).get();
- L694: .get();
- L702: const userRes = await db.collection('users').where({ openid: db.command.in(uniqueCoachOpenids) }).get();
- L769: const bookingResult = await db.collection(BOOKINGS).doc(bookingId).get();
- L790: await transaction.collection(BOOKINGS).doc(bookingId).update({
- L802: await transaction.collection(MEMBERSHIP_CARD).doc(booking.membershipCardId).update({

### 文件: cloudfunctions\notificationManagement\index.js
- L136: const result = await db.collection(NOTIFICATIONS).add({
- L203: .get();
- L217: .count();
- L265: .get();
- L282: await db.collection(NOTIFICATIONS).doc(notificationId).update({
- L324: }).update({
- L365: .get();
- L382: await db.collection(NOTIFICATIONS).doc(notificationId).remove();
- L418: }).count();
- L457: const { data: course } = await db.collection(COURSES).doc(courseId).get();
- L468: }).get();
- L476: }).get();
- L500: }).get();
- L505: }).get();
- L632: const { data: course } = await db.collection(COURSES).doc(courseId).get();
- L644: }).get();
- L650: }).get();
- L655: }).get();
- L736: }).get();
- L750: db.collection(NOTIFICATIONS).doc(notification._id).remove()
- L832: const result = await db.collection(NOTIFICATIONS).add({
- L894: .remove();
- L939: }).get();
- L958: }).get();
- L1002: }).update({

### 文件: cloudfunctions\notificationScheduler\index.js
- L97: }).get();
- L129: }).get();
- L139: }).get();
- L145: }).get();
- L179: userCourses.get(student.openid).courses.push(courseInfo);
- L190: coachCourses.get(coach.openid).courses.push(courseInfo);
- L299: .get();
- L312: }).get();
- L331: }).get();
- L341: }).get();
- L347: }).get();

### 文件: cloudfunctions\userManagement\auth.js
- L21: }).get();
- L63: }).get();
- L106: }).get();

### 文件: cloudfunctions\userManagement\index.js
- L75: }).get();
- L98: const addResult = await db.collection(USERS_COLLECTION).add({
- L126: await db.collection(USERS_COLLECTION).doc(existingUser._id).update({
- L175: }).get();
- L188: await db.collection(USERS_COLLECTION).doc(existingUser._id).update({
- L254: const userResult = await db.collection(USERS_COLLECTION).where({ openid }).get();
- L277: const updateResult = await db.collection(USERS_COLLECTION).doc(userId).update({
- L344: const userResult = await db.collection(USERS_COLLECTION).where({ openid }).get();
- L374: const updateResult = await db.collection(USERS_COLLECTION).doc(userId).update({
- L411: const userResult = await db.collection(USERS_COLLECTION).where({ openid }).get();
- L445: const readResult = await db.collection(USERS_COLLECTION).where({ openid }).get();
- L463: const testUpdateResult = await db.collection(USERS_COLLECTION).doc(user._id).update({
- L513: }).count();

### 文件: miniprogram\app.js
- L111: // wx.cloud.callFunction(): 调用云函数，类似于axios.post()或fetch()
- L112: const cloudResult = await wx.cloud.callFunction({

### 文件: miniprogram\pages\album-management\album-management.js
- L223: const result = await db.collection('album_folders').doc(folderId).get();
- L441: .get()
- L543: .get()
- L989: const directResult = await db.collection('album_folders').get();
- L1156: }).orderBy('createTime', 'desc').limit(200).get(),
- L1162: }).orderBy('createTime', 'desc').limit(20).get(),
- L1168: }).orderBy('bannerOrder', 'asc').limit(20).get(),
- L1173: }).orderBy('createTime', 'desc').limit(20).get()
- L1291: .get();
- L2094: imageFolders.forEach(folderId => allFolderIds.add(folderId));
- L2298: await db.collection('album_images').doc(imageId).update({
- L2786: db.collection('album_images').doc(img._id).update({
- L2849: return db.collection('album_images').add({
- L3027: }).get(),
- L3033: }).get(),
- L3039: }).get(),
- L3044: }).get()
- L3095: .count();
- L3104: .count();
- L3112: .count();
- L3122: .count();
- L3127: await db.collection('album_folders').doc(folderId).update({
- L3145: const trashFolder = await db.collection('album_folders').doc('folder_trash').get();
- L3215: return db.collection('album_images').doc(image._id).update({
- L3330: updateData.deletedTime = db.command.remove();
- L3331: updateData.beforeDeleteInfo = db.command.remove();
- L3335: await db.collection('album_images').doc(imageId).update({
- L3356: .get();
- L3361: return db.collection('album_images').doc(image._id).update({
- L3373: await db.collection('album_folders').doc(folderId).remove();
- L3409: const result = await db.collection('album_images').doc(imageId).get();
- L3485: .get();

### 文件: miniprogram\pages\coach-schedule\coach-schedule.js
- L89: const res = await wx.cloud.callFunction({
- L152: .get();
- L160: .get();
- L186: .get();
- L194: .get();
- L299: const res = await wx.cloud.callFunction({

### 文件: miniprogram\pages\course-detail\course-detail.js
- L509: const result = await db.collection('courses').doc(courseId).get();
- L573: .count();
- L603: .get();
- L609: .get();
- L804: .get();
- L812: .get();
- L868: const result = await wx.cloud.callFunction({

### 文件: miniprogram\pages\course-edit\course-edit.js
- L423: const res = await db.collection(collection).doc(id).get();
- L456: const res = await db.collection('coursesTemplate').doc(this.data.templateId).get();
- L592: const res = await db.collection('coachInfo').get();
- L621: }).get();
- L686: const res = await db.collection('coursesTemplate').get();
- L1364: const updateRes = await db.collection('coursesTemplate').doc(_id).update({
- L1375: const result = await db.collection('coursesTemplate').add({
- L1396: const result = await wx.cloud.callFunction({
- L1729: const updateRes = await db.collection('coursesTemplate').doc(_id).update({
- L1745: const result = await db.collection('coursesTemplate').add({
- L1856: .get();
- L1863: .get();

### 文件: miniprogram\pages\course-management\course-management.js
- L969: * 小程序通过wx.cloud.callFunction调用服务端的云函数
- L983: const res = await wx.cloud.callFunction({
- L2024: * .get(): 执行查询并获取结果
- L2031: .get();
- L2048: .get();
- L2106: .get();
- L2118: .get();
- L2251: const res = await db.collection('coursesTemplate').get();
- L3663: .count();
- L3921: const result = await wx.cloud.callFunction({
- L4147: * 使用doc(templateId).get()获取特定模板
- L4151: const templateRes = await db.collection('coursesTemplate').doc(templateId).get();
- L4218: const res = await wx.cloud.callFunction({
- L4246: .count();
- L4289: await db.collection('coursesTemplate').doc(templateId).remove();
- L4846: const result = await wx.cloud.callFunction({
- L5017: const result = await wx.cloud.callFunction({

### 文件: miniprogram\pages\index\index.js
- L763: .get()
- L835: .get()
- L924: const unreadResult = await wx.cloud.callFunction({
- L944: const coursesResult = await wx.cloud.callFunction({

### 文件: miniprogram\pages\membership-card-management\membership-card-management.js
- L360: const result = await wx.cloud.callFunction({
- L436: const result = await wx.cloud.callFunction({
- L558: const result = await wx.cloud.callFunction({
- L641: const result = await wx.cloud.callFunction({
- L714: const result = await wx.cloud.callFunction({
- L867: const result = await wx.cloud.callFunction({
- L981: const result = await wx.cloud.callFunction({
- L1027: const result = await wx.cloud.callFunction({
- L1091: const result = await wx.cloud.callFunction({
- L1136: .get();
- L1394: const result = await wx.cloud.callFunction({
- L1434: const result = await wx.cloud.callFunction({
- L1452: const result = await wx.cloud.callFunction({
- L1470: const result = await wx.cloud.callFunction({
- L1489: const result = await wx.cloud.callFunction({
- L1507: const result = await wx.cloud.callFunction({

### 文件: miniprogram\pages\membership-card\membership-card.js
- L37: .get();

### 文件: miniprogram\pages\my-bookings\my-bookings.js
- L671: }).get();
- L734: }).get();

### 文件: miniprogram\pages\notifications\notifications.js
- L197: const result = await wx.cloud.callFunction(callData);
- L313: const result = await wx.cloud.callFunction({
- L346: const result = await wx.cloud.callFunction({
- L394: const clearResult = await wx.cloud.callFunction({
- L645: const deleteResult = await wx.cloud.callFunction({

### 文件: miniprogram\pages\profile-edit\profile-edit.js
- L758: const res = await wx.cloud.callFunction({
- L917: const cloudResult = await wx.cloud.callFunction({

### 文件: miniprogram\pages\profile\profile.js
- L286: wx.cloud.callFunction({
- L348: const cloudResult = await wx.cloud.callFunction({
- L422: const cloudResult = await wx.cloud.callFunction({
- L568: const res = await wx.cloud.callFunction({
- L792: const cloudResult = await wx.cloud.callFunction({
- L849: const result = await wx.cloud.callFunction({

### 文件: miniprogram\pages\schedule\schedule.js
- L334: const res = await wx.cloud.callFunction({
- L453: if (item.name && item.name.indexOf(val) !== -1) set.add(item.name);
- L454: if (item.coach && item.coach.indexOf(val) !== -1) set.add(item.coach);
- L455: if (item.venue && item.venue.indexOf(val) !== -1) set.add(item.venue);
- L585: const res = await wx.cloud.callFunction({
- L929: const result = await wx.cloud.callFunction({
- L964: await wx.cloud.callFunction({

### 文件: miniprogram\pages\system-settings\system-settings.js
- L299: const result = await wx.cloud.callFunction({
- L454: const result = await wx.cloud.callFunction({

### 文件: miniprogram\pages\user-management\user-management.js
- L413: const res = await wx.cloud.callFunction({
- L715: const res = await wx.cloud.callFunction({
- L851: const res = await wx.cloud.callFunction({
- L985: const res = await wx.cloud.callFunction({
- L1040: const res = await wx.cloud.callFunction({

### 文件: miniprogram\utils\bookingUtils.js
- L235: .get();
- L302: const result = await wx.cloud.callFunction({

### 文件: miniprogram\utils\config.js
- L172: return configManager.get(key, defaultValue);
- L190: return await configManager.update(updates);

### 文件: miniprogram\utils\data-migration.js
- L53: const folderResult = await db.collection('album_folders').limit(1).get();
- L56: const imageResult = await db.collection('album_images').limit(1).get();
- L151: const result = await query.get();
- L213: await db.collection('album_images').doc(image._id).update({
- L242: .get();
- L266: .count();
- L275: .count();
- L284: .count();
- L289: await db.collection('album_folders').doc(folderId).update({

### 文件: miniprogram\utils\database.js
- L50: .get();
- L93: .get();
- L104: .count();
- L145: .get();         // 执行查询
- L170: // wx.cloud.callFunction调用微信云开发的云函数
- L171: const result = await wx.cloud.callFunction({
- L215: const result = await wx.cloud.callFunction({
- L268: .get();  // 执行查询
- L298: .get();  // 执行查询

### 文件: miniprogram\utils\folder-manager.js
- L16: .get();
- L40: .get();
- L50: const result = await db.collection('album_folders').add({
- L85: const folderResult = await db.collection('album_folders').doc(folderId).get();
- L106: .get();
- L116: await db.collection('album_folders').doc(folderId).update({
- L143: const folderResult = await db.collection('album_folders').doc(folderId).get();
- L162: await db.collection('album_folders').doc(folderId).remove();
- L184: const imageResult = await db.collection('album_images').doc(imageId).get();
- L206: await db.collection('album_images').doc(imageId).update({
- L241: .get();
- L245: await db.collection('album_images').doc(image._id).update({
- L254: const imageResult = await db.collection('album_images').doc(imageId).get();
- L266: await db.collection('album_images').doc(imageId).update({
- L296: const imageResult = await db.collection('album_images').doc(imageId).get();
- L307: await db.collection('album_images').doc(imageId).update({
- L346: .count();
- L355: .count();
- L363: .count();
- L372: .count();
- L377: await db.collection('album_folders').doc(folderId).update({

### 文件: miniprogram\utils\migration.js
- L15: // wx.cloud.callFunction调用微信云开发的云函数
- L16: const result = await wx.cloud.callFunction({

### 文件: miniprogram\utils\systemSettings.js
- L10: const result = await wx.cloud.callFunction({
- L43: const result = await wx.cloud.callFunction({
- L68: const result = await wx.cloud.callFunction({

### 文件: miniprogram\utils\trash-manager.js
- L14: const trashFolder = await db.collection('album_folders').doc('folder_trash').get();
- L64: const imageResult = await db.collection('album_images').doc(imageId).get();
- L76: return db.collection('album_images').doc(imageId).update({
- L121: const imageResult = await db.collection('album_images').doc(imageId).get();
- L130: deletedTime: db.command.remove(), // 移除删除时间字段
- L131: beforeDeleteInfo: db.command.remove(), // 移除备份信息
- L139: return db.collection('album_images').doc(imageId).update({
- L174: return db.collection('album_images').doc(imageId).get();
- L196: return db.collection('album_images').doc(imageId).remove();
- L226: .get();
- L284: .get();
- L328: .get();
- L369: .count();
- L380: .count();
- L405: const foldersResult = await db.collection('album_folders').get();
- L433: .count();
- L442: .count();
- L452: .count();
- L457: await db.collection('album_folders').doc(folderId).update({
