
// schedule.js
// 课程表页面逻辑文件
// 这是小程序的课程表页面，负责显示活动列表、预约管理、搜索筛选等功能
// 类似于Web应用的课程管理页面或移动应用的活动列表页面

/**
 * 模块导入说明
 *
 * 这里导入了多个工具模块，体现了模块化开发的思想：
 * 1. 数据库操作模块：处理数据的增删改查
 * 2. 常量定义模块：统一管理系统常量
 * 3. UI交互模块：统一的用户界面反馈
 * 4. 业务逻辑模块：封装复杂的业务操作
 *
 * 模块化的好处：
 * - 代码复用：同样的功能可以在多个页面使用
 * - 维护性：修改功能只需要修改一个地方
 * - 可测试性：每个模块可以独立测试
 * - 团队协作：不同开发者可以负责不同模块
 */

// 导入数据库操作相关函数
// 这些函数封装了与云数据库的交互逻辑
import { getCourseList, getUserBookings, getCourseBookings } from '../../utils/database.js';

// 导入系统常量定义
// 统一管理预约状态等常量，避免硬编码
import { BOOKING_STATUS } from '../../utils/constants.js';

// 导入Toast提示相关函数
// 提供统一的用户反馈体验
import { showToast, showLoading, hideToast } from '../../utils/toast.js';

// 导入预约相关业务逻辑函数
// 使用别名(as)避免命名冲突，bookCourse重命名为bookCourseUtil
import { bookCourse as bookCourseUtil, cancelBooking as cancelBookingUtil, loadSystemSettings } from '../../utils/bookingUtils.js';

/**
 * 工具函数：日期格式化
 *
 * 这些函数用于处理日期和时间的显示格式
 * 在JavaScript中，日期处理是常见且复杂的任务
 *
 * 函数设计原则：
 * 1. 防御性编程：检查输入参数的有效性
 * 2. 容错处理：无效输入返回空字符串而不是报错
 * 3. 格式统一：确保整个应用的日期格式一致
 */

/**
 * formatDate: 格式化日期为 YYYY-MM-DD 格式
 *
 * @param {string} dateStr - 日期字符串，如 "2025-01-15T10:30:00.000Z"
 * @returns {string} 格式化后的日期，如 "2025-01-15"
 *
 * 实现原理：
 * 1. 参数验证：检查输入是否为空
 * 2. 日期解析：使用Date构造函数解析日期字符串
 * 3. 有效性检查：使用isNaN检查日期是否有效
 * 4. 格式化输出：使用模板字符串和padStart方法格式化
 *
 * padStart方法说明：
 * - 字符串方法，用于在字符串开头填充字符
 * - (d.getMonth()+1).toString().padStart(2,'0')
 * - 将月份转为字符串，如果不足2位则在前面补0
 * - 例如：1 → "01"，12 → "12"
 */
function formatDate(dateStr) {
  // 防御性编程：检查输入参数
  if (!dateStr) return '';

  // 创建Date对象，解析日期字符串
  const d = new Date(dateStr);

  // 检查日期是否有效
  // isNaN(d.getTime())：如果日期无效，getTime()返回NaN
  if (isNaN(d.getTime())) return '';

  // 格式化为 YYYY-MM-DD 格式
  // 使用模板字符串和padStart确保格式统一
  return `${d.getFullYear()}-${(d.getMonth()+1).toString().padStart(2,'0')}-${d.getDate().toString().padStart(2,'0')}`;
}

/**
 * formatTime: 格式化时间为 HH:MM 格式
 *
 * @param {string} dateStr - 日期时间字符串
 * @returns {string} 格式化后的时间，如 "14:30"
 *
 * 用途：从完整的日期时间中提取时间部分
 * 例如："2025-01-15T14:30:00.000Z" → "14:30"
 */
function formatTime(dateStr) {
  if (!dateStr) return '';

  const date = new Date(dateStr);

  // 获取小时和分钟，并格式化为两位数
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');

  return `${hours}:${minutes}`;
}

/**
 * getWeekLabel: 获取星期标签
 *
 * @param {string} dateStr - 日期字符串
 * @returns {string} 星期标签，如 "周一"
 *
 * 实现原理：
 * 1. 使用数组映射：weekMap数组存储星期标签
 * 2. getDay()方法：返回0-6的数字，0表示周日，1表示周一...
 * 3. 数组索引访问：weekMap[d.getDay()]获取对应的中文标签
 *
 * 这种映射方式比if-else或switch语句更简洁
 */
function getWeekLabel(dateStr) {
  // 星期映射数组，索引0对应周日，索引1对应周一...
  const weekMap = ['周日','周一','周二','周三','周四','周五','周六'];
  const d = new Date(dateStr);

  // 使用数组索引获取对应的星期标签
  return weekMap[d.getDay()];
}

/**
 * Page()函数：注册课程表页面
 *
 * 这是小程序页面的核心函数，定义了页面的数据、生命周期和方法
 * 类似于其他框架的页面/组件定义：
 * - Vue: export default { data, methods, mounted, ... }
 * - React: class SchedulePage extends Component { state, componentDidMount, ... }
 * - Angular: @Component({ ... }) export class ScheduleComponent { ... }
 */
Page({
  /**
   * data: 页面数据对象
   *
   * 课程表页面采用了多视图设计，支持：
   * 1. 按日期筛选查看课程
   * 2. 查看当前可预约的活动
   * 3. 查看历史活动记录
   * 4. 搜索功能
   * 5. 分页加载优化性能
   *
   * 数据设计遵循以下原则：
   * - 单一数据源：所有状态集中管理
   * - 数据分离：原始数据与显示数据分离
   * - 性能优化：支持分页和懒加载
   */
  data: {
    // 视图切换标签页配置
    // 定义页面顶部的三个标签页选项
    // 数组类型，每个元素包含显示标签和对应的值
    viewTabs: [
      { label: '活动表', value: 'byDate' },     // 按日期筛选课程
      { label: '当前活动', value: 'current' },  // 显示当前可预约的活动
      { label: '历史活动', value: 'history' }   // 显示已结束的历史活动
    ],

    // 当前激活的视图
    // 字符串类型，对应viewTabs中的value值
    // 默认显示'byDate'（活动表）视图
    activeView: 'byDate',

    // 日期标签页数据
    // 在'活动表'视图下显示的日期选项
    // 动态生成，包含未来几天的日期选项
    dateTabs: [],

    // 选中的日期
    // 用户在日期筛选中选择的具体日期
    // 格式：'YYYY-MM-DD'，空字符串表示未选择
    selectedDate: '',

    // 课程列表（原始数据）
    // 从云数据库获取的完整课程数据
    // 作为数据源，不直接用于页面显示
    courseList: [],

    // 过滤后的课程列表（显示数据）
    // 根据当前视图、筛选条件等处理后的数据
    // 这是实际渲染到页面上的数据
    filteredCourseList: [],

    // 用户信息对象
    // 存储当前登录用户的信息
    // null表示未登录或信息未加载
    userInfo: null,

    // 取消预约时间限制
    // 从系统设置中获取的取消预约时间限制（小时）
    // 例如：24表示活动开始前24小时内不能取消预约
    cancelTimeLimit: null,

    // 空状态描述文字
    // 当没有课程数据时显示的提示文字
    // 可根据不同情况动态修改
    emptyDescription: '暂无活动',

    // 讲师ID（可选）
    // 如果从讲师页面跳转过来，会传递讲师ID
    // 用于筛选特定讲师的课程
    coachId: undefined,

    // 搜索关键词
    // 用户在搜索框中输入的文字
    // 用于按课程名称、讲师姓名等搜索
    searchValue: '',

    // 搜索结果列表
    // 根据搜索关键词筛选出的课程列表
    // 在搜索模式下显示此数据
    searchResultList: [],

    /**
     * 分页懒加载相关状态
     *
     * 性能优化策略：
     * 历史活动数据量可能很大，一次性加载会影响性能
     * 采用分页懒加载，用户滚动到底部时自动加载更多数据
     *
     * 分页原理：
     * 1. 首次加载第1页数据（前10条）
     * 2. 用户滚动到底部时，加载第2页数据
     * 3. 新数据追加到现有数据后面
     * 4. 直到没有更多数据为止
     */

    // 历史课程数据（已加载的）
    // 存储已经加载的历史活动数据
    // 随着用户滚动，数据会不断追加
    historyCourses: [],

    // 当前历史数据页码
    // 记录当前加载到第几页
    // 每次加载更多时页码+1
    historyPage: 1,

    // 每页数据条数
    // 控制每次加载的数据量
    // 10条是一个平衡值：不会太少（减少请求次数），也不会太多（避免单次加载时间过长）
    historyPageSize: 10,

    // 是否还有更多历史数据
    // 布尔值，true表示还有更多数据可以加载
    // false表示已经加载完所有数据
    historyHasMore: true,

    // 历史数据加载状态
    // 布尔值，true表示正在加载历史数据
    // 用于防止重复加载和显示加载状态
    historyLoading: false,

    // 时间轴分页相关状态 - 参考course-management
    visibleCurrentCourses: [], // 当前显示的当前活动列表
    allCurrentCourses: [], // 所有当前活动数据
    currentPageSize: 10, // 当前活动每页数据条数
    isLoadingCurrentBottom: false, // 当前活动底部加载状态
    noMoreCurrent: false, // 没有更多当前活动数据
    flashCurrentIndexes: [], // 新加载当前活动卡片的索引数组
    flashHistoryIndexes: [], // 新加载历史活动卡片的索引数组
  },

  async onLoad(options) {
    this.initDateTabs();
    if (options.coachId) {
      this.setData({ coachId: options.coachId });
    }
    const app = getApp();
    if (app.isLoggedIn()) {
      this.setData({ userInfo: app.getUserInfo() });
    }
    await this.loadSystemSettings();
    await this.refreshAllData();
  },

  async onShow() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({ selected: 1 });
    }
    const app = getApp();
    if (app.isLoggedIn()) {
      this.setData({ userInfo: app.getUserInfo() });
      // 检查新课程并更新Badge
      await this.checkNewCourses();
    }
    await this.loadSystemSettings();
    await this.refreshAllData();

    // 用户查看了课程表，更新查看时间（清除Badge）
    if (app.isLoggedIn()) {
      await this.updateScheduleViewTime();
    }
  },

  async onPullDownRefresh() {
    await this.loadSystemSettings();
    await this.refreshAllData();
    wx.stopPullDownRefresh();
  },

  // 初始化日期tab
  initDateTabs() {
    const today = new Date();
    const dateTabs = [];
    for (let i = 0; i < 7; i++) {
      const d = new Date(today);
      d.setDate(today.getDate() + i);
      const month = (d.getMonth() + 1).toString().padStart(2, '0');
      const day = d.getDate().toString().padStart(2, '0');
      const dateStr = `${d.getFullYear()}-${month}-${day}`;
      dateTabs.push({
        label: getWeekLabel(d),
        date: `${month}/${day}`,
        value: dateStr
      });
    }
    this.setData({
      dateTabs,
      selectedDate: dateTabs[0].value
    });
  },

  // 统一数据加载入口
  async refreshAllData() {
    showLoading(this, '加载中...');
    try {
      const app = getApp();
      const userId = app.isLoggedIn() ? app.getUserInfo().openid : '';
      // 一次性获取全部数据
      const res = await wx.cloud.callFunction({
        name: 'bookingManagement',
        data: {
          action: 'getScheduleData',
          data: { userId }
        }
      });
      if (!res.result.success) {
        showToast(this, { message: res.result.message || '数据加载失败', theme: 'error' });
        return;
      }
      const { courses, bookings, userBookings, userMap } = res.result.data;
      // 预处理课程数据
      const now = new Date();
      const courseBookingCounts = {};
      bookings.forEach(b => {
        if (b.status === 'upcoming') {
          courseBookingCounts[b.courseId] = (courseBookingCounts[b.courseId] || 0) + 1;
        }
      });
      const courseList = courses.map(course => {
        const isBooked = userBookings.some(b => String(b.courseId) === String(course._id) && b.status === 'upcoming');
        const startTime = new Date(course.startTime);
        const endTime = new Date(course.endTime);
        const started = now >= startTime;
        const ended = now >= endTime;
        const bookingCount = courseBookingCounts[course._id] || 0;
        const remaining = course.capacity - bookingCount;

        // 修正状态判断逻辑
        const inProgress = started && !ended;  // 进行中：已开始但未结束
        const isFull = remaining <= 0;         // 已满：剩余名额≤0
        const available = remaining > 0 && !started && !ended;  // 可预约：有名额且未开始且未结束

        let coachNames = [];
        if (Array.isArray(course.coach)) {
          coachNames = course.coach.map(openid => userMap[openid]?.nickName || openid.slice(-4));
        }
        return {
          ...course,
          id: course._id,
          isBooked,
          formattedDate: formatDate(course.startTime),
          time: `${formatTime(course.startTime)}-${formatTime(course.endTime)}`,
          coach: coachNames.join('、'),
          remaining,
          started,
          ended,
          inProgress,
          isFull,
          available,
          startTS: startTime.getTime(),
          startTime: startTime.toISOString(),
          endTime: endTime.toISOString()
        };
      });
      courseList.sort((a, b) => a.startTS - b.startTS);
      this.setData({ courseList });
      this._filterAndSetCourses(); // 初始化时也进行过滤
    } finally {
      hideToast(this);
    }
  },

  // 只做本地过滤，不 setData
  _filterCourses(courseList = this.data.courseList) {
    const now = new Date();
    return courseList.filter(course => {
      if (this.data.activeView === 'current') {
        // 当前活动：只过滤已结束，且不过滤日期
        return new Date(course.endTime) >= now;
      }
      if (this.data.activeView === 'history') {
        return new Date(course.endTime) < now;
      }
      // 仅活动表视图下才按日期筛选
      if (this.data.activeView === 'byDate' && this.data.selectedDate) {
        const courseDate = new Date(course.startTime).toISOString().split('T')[0];
        if (courseDate !== this.data.selectedDate) return false;
        if (new Date(course.endTime) < now) return false; // 只显示未结束
      }
      if (this.data.coachId && !course.coach.includes(this.data.coachId)) return false;
      return true;
    });
  },

  // 搜索输入变化（原t-search组件的事件）
  onSearchChange(e) {
    const value = e.detail.value;
    this.setData({ searchValue: value }, () => {
      this._updateSearchResultList();
      this._filterAndSetCourses();
    });
  },

  // 搜索输入变化（新的自定义搜索框的事件）
  // 与course-management页面的onSearchInput方法保持一致
  onSearchInput(e) {
    const value = e.detail.value;
    this.setData({ searchValue: value }, () => {
      this._filterAndSetCourses();
    });
  },
  // 预览建议列表
  _updateSearchResultList() {
    if (!this.data.searchValue) {
      this.setData({ searchResultList: [] });
      return;
    }
    // 只在当前活动视图下提供建议，且基于当前活动下所有已上线课程
    if (this.data.activeView !== 'current') {
      this.setData({ searchResultList: [] });
      return;
    }
    const val = this.data.searchValue.trim();
    const set = new Set();
    // 只基于当前活动下所有已上线课程
    const currentCourses = this.data.courseList.filter(item => new Date(item.endTime) >= new Date());
    currentCourses.forEach(item => {
      if (item.name && item.name.indexOf(val) !== -1) set.add(item.name);
      if (item.coach && item.coach.indexOf(val) !== -1) set.add(item.coach);
      if (item.venue && item.venue.indexOf(val) !== -1) set.add(item.venue);
    });
    this.setData({ searchResultList: Array.from(set).slice(0, 8) }); // 最多8条
  },
  // 选择建议
  onSearchSuggestionTap(e) {
    const value = e.detail.value;
    this.setData({ searchValue: value }, () => {
      this._filterAndSetCourses();
      this._updateSearchResultList();
    });
  },
  // 清空搜索
  onSearchClear() {
    this.setData({ searchValue: '' }, this._filterAndSetCourses);
  },
  // 回车/提交搜索
  onSearchSubmit(e) {
    this.setData({ searchValue: e.detail.value }, this._filterAndSetCourses);
  },
  // 过滤并 setData
  _filterAndSetCourses() {
    let filtered = this._filterCourses();
    // 仅当前活动视图下做搜索过滤
    if (this.data.activeView === 'current' && this.data.searchValue) {
      const val = this.data.searchValue.trim();
      filtered = filtered.filter(item => {
        return (
          (item.name && item.name.indexOf(val) !== -1) ||
          (item.coach && item.coach.indexOf(val) !== -1) ||
          (item.venue && item.venue.indexOf(val) !== -1)
        );
      });
    }

    // 当前活动视图：使用时间轴分页模式
    if (this.data.activeView === 'current') {
      this._setupCurrentCoursesTimeline(filtered);
    } else {
      // 其他视图保持原有逻辑
      const emptyDescription = this._generateEmptyDescription(filtered, this.data.courseList);
      this.setData({ filteredCourseList: filtered, emptyDescription });
    }
  },

  // 设置当前活动时间轴分页 - 参考course-management
  _setupCurrentCoursesTimeline(courses) {
    // 为每个课程添加时间轴日期字段
    const coursesWithTimeline = courses.map(item => {
      let timelineDate = '';
      if (item.startTime) {
        const date = new Date(item.startTime);
        const y = date.getFullYear();
        const m = date.getMonth() + 1;
        const d = date.getDate();
        const weekMap = ['周日','周一','周二','周三','周四','周五','周六'];
        const week = weekMap[date.getDay()];
        timelineDate = `${y}年${m}月${d}日 ${week}`;
      }
      return {
        ...item,
        timelineDate
      };
    });

    // 按时间排序：最早的在前
    coursesWithTimeline.sort((a, b) => {
      const timeA = a.startTime ? new Date(a.startTime).getTime() : 0;
      const timeB = b.startTime ? new Date(b.startTime).getTime() : 0;
      return timeA - timeB;
    });

    // 设置时间轴分页数据
    const initialVisible = coursesWithTimeline.slice(0, this.data.currentPageSize);
    this.setData({
      allCurrentCourses: coursesWithTimeline,
      visibleCurrentCourses: initialVisible,
      noMoreCurrent: initialVisible.length >= coursesWithTimeline.length,
      emptyDescription: this._generateEmptyDescription(coursesWithTimeline, this.data.courseList)
    });
  },

  // 事件：切换日期
  onDateTabChange(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({ selectedDate: value }, () => {
      this._filterAndSetCourses();
    });
  },
  // 事件：切换视图，切到历史活动时初始化分页加载
  // 修改为适配TDesign t-tabs组件的事件处理方式
  onViewChange(e) {
    // TDesign t-tabs组件的事件对象结构：
    // e.detail.value 包含选中的tab值
    // 与原来的 e.currentTarget.dataset.value 不同
    const activeView = e.detail.value;

    this.setData({ activeView }, async () => {
      if (activeView === 'history') {
        // 初始化历史活动分页
        this.setData({
          historyCourses: [],
          historyPage: 1,
          historyHasMore: true,
          flashHistoryIndexes: [],
          emptyDescription: '正在加载历史活动...'
        });
        await this.loadHistoryCourses();
      } else if (activeView === 'current') {
        // 初始化当前活动时间轴
        this.setData({
          visibleCurrentCourses: [],
          allCurrentCourses: [],
          noMoreCurrent: false,
          flashCurrentIndexes: []
        });
        this._filterAndSetCourses();
      } else {
        // 其他视图
        this._filterAndSetCourses();
      }
    });
  },

  // 分页加载历史活动
  async loadHistoryCourses() {
    if (this.data.historyLoading || !this.data.historyHasMore) return;
    this.setData({ historyLoading: true });
    try {
      // 云函数/接口不再传 userId
      const res = await wx.cloud.callFunction({
        name: 'bookingManagement',
        data: {
          action: 'getHistoryCourses',
          data: {
            page: this.data.historyPage,
            pageSize: this.data.historyPageSize
          }
        }
      });
      if (!res.result.success) {
        showToast(this, { message: res.result.message || '加载失败', theme: 'error' });
        return;
      }
      let list = res.result.data.list || [];
      // 按结束时间倒序，给每条加 date 字段（YYYY年M月D日 周X）
      list = list.map(item => {
        let dateObj = null;

        // 优先使用 formattedDate
        if (item.formattedDate) {
          dateObj = new Date(item.formattedDate);
        }
        // 其次使用 endTime
        else if (item.endTime) {
          dateObj = new Date(item.endTime);
        }

        // 如果日期无效，使用当前日期
        if (!dateObj || isNaN(dateObj.getTime())) {
          dateObj = new Date();
        }

        const y = dateObj.getFullYear();
        const m = dateObj.getMonth() + 1;
        const d = dateObj.getDate();
        const weekMap = ['周日','周一','周二','周三','周四','周五','周六'];
        const week = weekMap[dateObj.getDay()];

        return {
          ...item,
          date: `${y}年${m}月${d}日 ${week}`
        };
      });
      const currentLength = this.data.historyCourses.length;
      const newHistoryCourses = this.data.historyCourses.concat(list);

      // 设置空状态描述
      let emptyDescription = this.data.emptyDescription;
      if (newHistoryCourses.length === 0) {
        emptyDescription = '暂无历史活动';
      }

      this.setData({
        historyCourses: newHistoryCourses,
        historyHasMore: list.length === this.data.historyPageSize,
        historyPage: this.data.historyPage + 1,
        flashHistoryIndexes: Array.from({length: list.length}, (_, i) => currentLength + i),
        emptyDescription
      });
      // 1秒后清除新加载提示（动画完成后）
      setTimeout(() => this.setData({ flashHistoryIndexes: [] }), 1000);
    } finally {
      this.setData({ historyLoading: false });
    }
  },

  // 页面滚动到底部事件，历史活动tab下触发分页加载
  onReachBottom() {
    if (this.data.activeView === 'history') {
      this.loadHistoryCourses();
    }
  },

  // 当前活动时间轴滚动到底部事件 - 参考course-management
  onCurrentScrollToLower() {
    if (this.data.isLoadingCurrentBottom) return;

    const { visibleCurrentCourses, allCurrentCourses, currentPageSize } = this.data;
    const last = visibleCurrentCourses[visibleCurrentCourses.length - 1];

    if (!last) return;

    const lastIdx = allCurrentCourses.findIndex(c => c.id === last.id);
    if (lastIdx === -1 || lastIdx === allCurrentCourses.length - 1) {
      this.setData({ noMoreCurrent: true });
      return; // 没有更多
    }

    this.setData({ isLoadingCurrentBottom: true, noMoreCurrent: false });
    setTimeout(() => {
      const next = allCurrentCourses.slice(lastIdx + 1, lastIdx + 1 + currentPageSize);
      this.setData({
        visibleCurrentCourses: visibleCurrentCourses.concat(next),
        isLoadingCurrentBottom: false,
        flashCurrentIndexes: Array.from({length: next.length}, (_, i) => visibleCurrentCourses.length + i),
        noMoreCurrent: lastIdx + 1 + currentPageSize >= allCurrentCourses.length
      });
      // 1秒后清除新加载提示（动画完成后）
      setTimeout(() => this.setData({ flashCurrentIndexes: [] }), 1000);
    }, 600);
  },

  // 预约课程（本地 patch + 后台刷新）
  async bookCourse(e) {
    const app = getApp();
    if (!app.isLoggedIn()) {
      wx.showModal({
        title: '未登录',
        content: '您还未登录，是否前往登录？',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({ url: '/pages/profile/profile' });
          }
        }
      });
      return;
    }
    const course = e.currentTarget.dataset.course;
    await bookCourseUtil(course, this,
      // 成功回调
      () => {
        // 本地 patch - 更新状态计算逻辑
        const courseList = this.data.courseList.map(c => {
          if (c.id === course.id) {
            const newRemaining = c.remaining - 1;
            const now = new Date();
            const startTime = new Date(c.startTime);
            const endTime = new Date(c.endTime);
            const started = now >= startTime;
            const ended = now >= endTime;
            const inProgress = started && !ended;
            const isFull = newRemaining <= 0;
            const available = newRemaining > 0 && !started && !ended;

            return {
              ...c,
              isBooked: true,
              remaining: newRemaining,
              inProgress,
              isFull,
              available
            };
          }
          return c;
        });

        // 如果是当前活动视图，同时更新时间轴数据
        if (this.data.activeView === 'current') {
          const allCurrentCourses = this.data.allCurrentCourses.map(c => {
            if (c.id === course.id) {
              const newRemaining = c.remaining - 1;
              const now = new Date();
              const startTime = new Date(c.startTime);
              const endTime = new Date(c.endTime);
              const started = now >= startTime;
              const ended = now >= endTime;
              const inProgress = started && !ended;
              const isFull = newRemaining <= 0;
              const available = newRemaining > 0 && !started && !ended;

              return {
                ...c,
                isBooked: true,
                remaining: newRemaining,
                inProgress,
                isFull,
                available
              };
            }
            return c;
          });
          const visibleCurrentCourses = this.data.visibleCurrentCourses.map(c => {
            if (c.id === course.id) {
              const newRemaining = c.remaining - 1;
              const now = new Date();
              const startTime = new Date(c.startTime);
              const endTime = new Date(c.endTime);
              const started = now >= startTime;
              const ended = now >= endTime;
              const inProgress = started && !ended;
              const isFull = newRemaining <= 0;
              const available = newRemaining > 0 && !started && !ended;

              return {
                ...c,
                isBooked: true,
                remaining: newRemaining,
                inProgress,
                isFull,
                available
              };
            }
            return c;
          });
          this.setData({ courseList, allCurrentCourses, visibleCurrentCourses });
        } else {
          const filteredCourseList = this._filterCourses(courseList);
          this.setData({ courseList, filteredCourseList });
        }

        // 后台刷新
        this.refreshAllData();
      },
      // 错误回调
      (error) => {
        console.error('预约失败:', error);
      }
    );
  },



  // 取消预约（本地 patch + 后台刷新）
  async cancelBooking(e) {
    const course = e.currentTarget.dataset.course;
    await cancelBookingUtil(course, this,
      // 成功回调
      (course) => {
        // 本地 patch - 更新状态计算逻辑
        const courseList = this.data.courseList.map(c => {
          if (c.id === course.id) {
            const newRemaining = c.remaining + 1;
            const now = new Date();
            const startTime = new Date(c.startTime);
            const endTime = new Date(c.endTime);
            const started = now >= startTime;
            const ended = now >= endTime;
            const inProgress = started && !ended;
            const isFull = newRemaining <= 0;
            const available = newRemaining > 0 && !started && !ended;

            return {
              ...c,
              isBooked: false,
              remaining: newRemaining,
              inProgress,
              isFull,
              available
            };
          }
          return c;
        });

        // 如果是当前活动视图，同时更新时间轴数据
        if (this.data.activeView === 'current') {
          const allCurrentCourses = this.data.allCurrentCourses.map(c => {
            if (c.id === course.id) {
              const newRemaining = c.remaining + 1;
              const now = new Date();
              const startTime = new Date(c.startTime);
              const endTime = new Date(c.endTime);
              const started = now >= startTime;
              const ended = now >= endTime;
              const inProgress = started && !ended;
              const isFull = newRemaining <= 0;
              const available = newRemaining > 0 && !started && !ended;

              return {
                ...c,
                isBooked: false,
                remaining: newRemaining,
                inProgress,
                isFull,
                available
              };
            }
            return c;
          });
          const visibleCurrentCourses = this.data.visibleCurrentCourses.map(c => {
            if (c.id === course.id) {
              const newRemaining = c.remaining + 1;
              const now = new Date();
              const startTime = new Date(c.startTime);
              const endTime = new Date(c.endTime);
              const started = now >= startTime;
              const ended = now >= endTime;
              const inProgress = started && !ended;
              const isFull = newRemaining <= 0;
              const available = newRemaining > 0 && !started && !ended;

              return {
                ...c,
                isBooked: false,
                remaining: newRemaining,
                inProgress,
                isFull,
                available
              };
            }
            return c;
          });
          this.setData({ courseList, allCurrentCourses, visibleCurrentCourses });
        } else {
          const filteredCourseList = this._filterCourses(courseList);
          this.setData({ courseList, filteredCourseList });
        }

        // 后台刷新
        this.refreshAllData();
      },
      // 错误回调
      (error) => {
        console.error('取消失败:', error);
      }
    );
  },

  // 跳转详情
  goToCourseDetail(e) {
    const course = e.currentTarget.dataset.course;
    wx.navigateTo({ url: `/pages/course-detail/course-detail?id=${course.id}` });
  },

  // 系统设置
  async loadSystemSettings() {
    try {
      await loadSystemSettings(this);
    } catch (error) {
      console.error('加载系统设置失败:', error);
    }
  },

  // 空状态描述
  _generateEmptyDescription(filteredCourseList, courseList) {
    if (!courseList || courseList.length === 0) return '暂无活动';
    if (!filteredCourseList || filteredCourseList.length === 0) {
      if (this.data.selectedDate) {
        const dateObj = new Date(this.data.selectedDate);
        const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
        const day = dateObj.getDate().toString().padStart(2, '0');
        const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        const weekday = weekdays[dateObj.getDay()];
        return `${month}月${day}日 ${weekday} 暂无活动`;
      }
      return '暂无活动';
    }
    return '';
  },

  /**
   * 检查是否有新课程
   * 用于显示Badge徽标
   */
  async checkNewCourses() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      if (!userInfo || !userInfo.openid) {
        return;
      }

      const result = await wx.cloud.callFunction({
        name: 'notificationManagement',
        data: {
          action: 'checkNewCourses',
          data: {
            userId: userInfo.openid
          }
        }
      });

      if (result.result.success) {
        const hasNewCourses = result.result.data.hasNewCourses || false;

        // 更新tabBar的Badge状态
        if (typeof this.getTabBar === 'function' && this.getTabBar()) {
          this.getTabBar().setScheduleBadge(hasNewCourses);
        }
      }
    } catch (error) {
      console.error('检查新课程失败:', error);
      // 检查失败时不显示错误提示，避免影响用户体验
    }
  },

  /**
   * 更新用户查看课程表的时间
   * 用于清除Badge徽标
   */
  async updateScheduleViewTime() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      if (!userInfo || !userInfo.openid) {
        return;
      }

      await wx.cloud.callFunction({
        name: 'notificationManagement',
        data: {
          action: 'updateScheduleViewTime',
          data: {
            userId: userInfo.openid
          }
        }
      });

      // 清除Badge
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().setScheduleBadge(false);
      }
    } catch (error) {
      console.error('更新查看时间失败:', error);
      // 更新失败时不显示错误提示，避免影响用户体验
    }
  },

  // 移除自定义弹窗处理方法，使用系统默认弹窗
});